/**
 * Централизованный инициализатор приложения
 * Отвечает за последовательную загрузку и инициализацию всех компонентов
 */

import { tanksData } from '../data/tanks.js';
import { TankSearchIndex } from '../services/SearchService.js';
import { state } from '../store/state.js';
import { setupSidebarMenuItems, hideAllSections, showSection } from '../utils/ui.js';
import { initRouter } from '../router/index.js';
import { performanceMonitor, getCachedElement } from '../utils/performance.js';

export class AppInitializer {
  constructor() {
    this.searchIndex = null;
    this.isInitialized = false;
  }

  /**
   * Основная функция инициализации приложения
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      console.log('[Init] 🚀 Starting optimized app initialization...');
      
      this.showLoadingIndicator();

      // Фаза 1: Критические компоненты
      console.log('[Init] Phase 1: Critical components');
      this.cacheDOMElements();

      // Фаза 2: Загрузка данных танков
      console.log('[Init] Phase 2: Loading tank data');
      await this.initializeAllTanksCacheOptimized();

      // Фаза 3: Построение поискового индекса
      console.log('[Init] Phase 3: Building search index');
      await this.buildSearchIndexAsync();

      // Фаза 4: UI компоненты
      console.log('[Init] Phase 4: UI components');
      await this.initializeUIComponents();

      // Фаза 5: Роутер и события
      console.log('[Init] Phase 5: Router and events');
      await this.initializeRouterAndEvents();

      // Фаза 6: Дополнительные компоненты (lazy loading)
      console.log('[Init] Phase 6: Additional components');
      this.initializeAdditionalComponents();

      this.hideLoadingIndicator();
      this.isInitialized = true;

      console.log('[Init] ✅ App initialization completed successfully');
    } catch (error) {
      console.error('[Init] ❌ App initialization failed:', error);
      this.showErrorMessage('Ошибка инициализации приложения');
    }
  }

  /**
   * Оптимизированная инициализация данных танков с батчингом
   */
  async initializeAllTanksCacheOptimized() {
    if (state.allTanks && state.allTanks.length) return;

    const BATCH_SIZE = 50;
    const flattened = [];
    const processedTanks = new Set();
    let processed = 0;
    let total = 0;

    // Подсчитываем общее количество танков
    Object.values(tanksData).forEach(types => {
      Object.values(types).forEach(tanks => {
        total += tanks.length;
      });
    });

    console.log(`[Init] Processing ${total} tanks in batches of ${BATCH_SIZE}`);

    for (const [country, types] of Object.entries(tanksData)) {
      for (const [type, tanks] of Object.entries(types)) {
        for (let i = 0; i < tanks.length; i += BATCH_SIZE) {
          const batch = tanks.slice(i, i + BATCH_SIZE);

          batch.forEach(tank => {
            const tankKey = `${tank.name}_${country}_${type}`;

            if (!processedTanks.has(tankKey)) {
              flattened.push({ ...tank, country, type });
              processedTanks.add(tankKey);
              processed++;
            }
          });

          this.updateLoadingProgress(processed, total);

          if (i + BATCH_SIZE < tanks.length) {
            await new Promise(resolve => setTimeout(resolve, 0));
          }
        }
      }
    }

    state.allTanks = flattened;
    console.log(`[Init] ✅ Loaded ${flattened.length} unique tanks`);
  }

  /**
   * Асинхронное построение поискового индекса
   */
  async buildSearchIndexAsync() {
    return new Promise(resolve => {
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(() => {
          console.log('[Init] Building search index...');
          this.searchIndex = new TankSearchIndex(state.allTanks);
          console.log('[Init] ✅ Search index built');
          resolve();
        });
      } else {
        setTimeout(() => {
          console.log('[Init] Building search index...');
          this.searchIndex = new TankSearchIndex(state.allTanks);
          console.log('[Init] ✅ Search index built');
          resolve();
        }, 0);
      }
    });
  }

  /**
   * Инициализация UI компонентов с requestAnimationFrame
   */
  async initializeUIComponents() {
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        setupSidebarMenuItems();
        this.restoreAppStateAfterReload();
        resolve();
      });
    });
  }

  /**
   * Инициализация роутинга и событий
   */
  async initializeRouterAndEvents() {
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        // Инициализация будет делегирована EventManager
        initRouter({
          initializeAllTanksCache: () => this.initializeAllTanksCacheOptimized(),
          cacheDOMElements: () => this.cacheDOMElements(),
        });
        resolve();
      });
    });
  }

  /**
   * Дополнительные компоненты (lazy loading)
   */
  initializeAdditionalComponents() {
    try {
      // Lazy loading дополнительных компонентов
      setTimeout(() => {
        import('../components/CompareSystem.js').then(module => {
          module.initializeCompareSystem();
        });
      }, 1000);

      setTimeout(() => {
        import('../components/SettingsManager.js').then(module => {
          module.initializeSettingsHandlers();
        });
      }, 1500);

      console.log('[Init] ✅ Additional components scheduled for lazy loading');
    } catch (error) {
      console.warn('[Init] ⚠️ Error in additional components:', error);
    }
  }

  /**
   * Кэширование DOM элементов
   */
  cacheDOMElements() {
    performanceMonitor.startMeasure('DOM_CACHING');
    console.log('🔄 Caching DOM elements...');

    // Кэшируем основные элементы
    getCachedElement('#tank-list');
    getCachedElement('#tank-search');
    getCachedElement('#tank-characteristics-container');
    getCachedElement('#tank-error');
    getCachedElement('#build-modal');

    performanceMonitor.endMeasure('DOM_CACHING');
    console.log('✅ DOM elements cached successfully.');
  }

  /**
   * Функция восстановления состояния после F5
   */
  restoreAppStateAfterReload() {
    console.log('[Init] Restoring app state after reload...');

    const savedState = localStorage.getItem('appState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        console.log('[Init] Found saved state:', parsedState);

        if (Date.now() - parsedState.timestamp < 5 * 60 * 1000) {
          if (parsedState.activeTab === 'vehicles' && parsedState.vehiclesVisible) {
            console.log('[Init] Restoring vehicles tab state');
            showSection('vehicles');
          }
        }
      } catch (error) {
        console.warn('[Init] Error parsing saved state:', error);
      }
    }

    // Проверяем hash для характеристик танка
    const hash = window.location.hash;
    if (hash && hash.startsWith('#')) {
      const tankName = decodeURIComponent(hash.substring(1));
      console.log(`[Init] Found tank hash: ${tankName}`);
      
      setTimeout(() => {
        import('../components/TankCharacteristics.js').then(module => {
          module.showTankCharacteristics(tankName);
        });
      }, 200);
    }
  }

  /**
   * Показать индикатор загрузки
   */
  showLoadingIndicator() {
    const loadingHTML = `
      <div id="app-loading-overlay" class="loading-overlay">
        <div class="loading-container">
          <div class="loading-spinner"></div>
          <div class="loading-text">Загрузка танков...</div>
          <div class="loading-progress">
            <div class="progress-bar">
              <div id="progress-fill" class="progress-fill"></div>
            </div>
            <div id="progress-text" class="progress-text">0%</div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', loadingHTML);
  }

  /**
   * Скрыть индикатор загрузки
   */
  hideLoadingIndicator() {
    const overlay = document.getElementById('app-loading-overlay');
    if (overlay) {
      overlay.style.opacity = '0';
      setTimeout(() => overlay.remove(), 300);
    }
  }

  /**
   * Обновить прогресс загрузки
   */
  updateLoadingProgress(current, total) {
    const percentage = Math.round((current / total) * 100);
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    if (progressFill) progressFill.style.width = `${percentage}%`;
    if (progressText) progressText.textContent = `${percentage}%`;
  }

  /**
   * Показать сообщение об ошибке
   */
  showErrorMessage(message) {
    const errorHTML = `
      <div id="app-error-overlay" class="error-overlay">
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">${message}</div>
          <button onclick="location.reload()" class="error-button">Обновить страницу</button>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', errorHTML);
  }

  /**
   * Получить поисковый индекс
   */
  getSearchIndex() {
    return this.searchIndex;
  }
}

// Экспортируем единственный экземпляр
export const appInitializer = new AppInitializer();
