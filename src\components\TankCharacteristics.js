/**
 * Компонент для отображения характеристик танков
 * Отвечает за показ детальной информации о танке
 */

import { state } from '../store/state.js';
import { getTankIconPath } from '../utils/constants.js';
import { hideAllSections } from '../utils/ui.js';
import { updateTankDetailsUI } from './tank-details/TankDetails.js';

/**
 * Создание ID танка на основе имени
 */
function createTankId(tankName) {
  const iconPath = getTankIconPath(tankName);
  return iconPath.split('/').pop().replace('.webp', '');
}

/**
 * Показать характеристики танка по имени
 */
export function showTankCharacteristics(tankName) {
  const tankId = createTankId(tankName);
  showTankCharacteristicsById(tankId);
}

/**
 * Показать характеристики танка по ID
 */
export function showTankCharacteristicsById(tankId) {
  const tank = state.allTanks.find(t => createTankId(t.name) === tankId);
  if (!tank) {
    console.error(`Tank with ID ${tankId} not found`);
    return;
  }

  state.selectedTank = tank;
  window.currentTankId = tankId;

  hideAllSections();

  const characteristicsContainer = document.getElementById('tank-characteristics-container');
  if (!characteristicsContainer) {
    console.error('Tank characteristics container not found!');
    return;
  }

  // Временно скрываем контейнер для плавной анимации
  characteristicsContainer.classList.add('hidden');
  characteristicsContainer.style.display = 'none';

  // Загружаем и обновляем детали танка
  updateTankDetailsUI(tank);

  // Показываем контейнер с анимацией
  setTimeout(() => {
    characteristicsContainer.classList.remove('hidden');
    characteristicsContainer.style.display = 'block';
    characteristicsContainer.style.opacity = '1';
    characteristicsContainer.style.visibility = 'visible';
  }, 100);
}

/**
 * Инициализация роутера для характеристик танков
 */
export function initTankCharacteristicsRouter() {
  // Проверяем hash при загрузке
  const hash = window.location.hash;
  if (hash && hash.startsWith('#')) {
    const tankName = decodeURIComponent(hash.substring(1));
    const tankId = createTankId(tankName);

    setTimeout(() => {
      if (state.allTanks && state.allTanks.length > 0) {
        showTankCharacteristicsById(tankId);
      }
    }, 500);
  }

  // Обработчик изменения hash
  window.addEventListener('hashchange', () => {
    const newHash = window.location.hash;
    if (newHash && newHash.startsWith('#')) {
      const tankName = decodeURIComponent(newHash.substring(1));
      const tankId = createTankId(tankName);
      showTankCharacteristicsById(tankId);
    } else {
      hideAllSections();
      import('../utils/ui.js').then(module => {
        module.showSection('vehicles');
      });
    }
  });

  console.log('✅ Tank characteristics router initialized');
}

/**
 * Скрыть характеристики танка
 */
export function hideTankCharacteristics() {
  const characteristicsContainer = document.getElementById('tank-characteristics-container');
  if (characteristicsContainer) {
    characteristicsContainer.classList.add('hidden');
    characteristicsContainer.style.display = 'none';
    characteristicsContainer.style.opacity = '0';
    characteristicsContainer.style.visibility = 'hidden';
  }
}

/**
 * Показать список танков (возврат из характеристик)
 */
export function showTankList() {
  state.selectedTank = null;
  localStorage.removeItem('selectedTank');
  window.location.hash = '';
  
  hideTankCharacteristics();
  
  const tankListElement = document.getElementById('tank-list');
  if (tankListElement) {
    tankListElement.classList.remove('hidden');
    tankListElement.style.display = 'grid';
    tankListElement.style.opacity = '1';
    tankListElement.style.visibility = 'visible';
  }

  const backButton = document.getElementById('back-to-list-btn');
  if (backButton) {
    backButton.classList.add('hidden');
  }

  // Обновляем список танков
  import('../core/TankManager.js').then(module => {
    module.tankManager.updateTankList();
  });
}

/**
 * Инициализация кнопки "Назад к списку"
 */
export function initBackToListButton() {
  const backButton = document.getElementById('back-to-list-btn');
  if (backButton) {
    backButton.addEventListener('click', showTankList);
  }

  const backIcon = document.getElementById('back-icon');
  if (backIcon) {
    backIcon.addEventListener('click', showTankList);
  }
}

/**
 * Проверка, показаны ли сейчас характеристики танка
 */
export function isShowingTankCharacteristics() {
  const characteristicsContainer = document.getElementById('tank-characteristics-container');
  return characteristicsContainer && !characteristicsContainer.classList.contains('hidden');
}

/**
 * Получить текущий выбранный танк
 */
export function getCurrentTank() {
  return state.selectedTank;
}

/**
 * Установить текущий танк
 */
export function setCurrentTank(tank) {
  state.selectedTank = tank;
  if (tank) {
    localStorage.setItem('selectedTank', JSON.stringify({
      name: tank.name,
      country: tank.country,
      type: tank.type,
    }));
  } else {
    localStorage.removeItem('selectedTank');
  }
}

/**
 * Восстановить выбранный танк из localStorage
 */
export function restoreSelectedTank() {
  const savedTank = localStorage.getItem('selectedTank');
  if (savedTank) {
    try {
      const tankData = JSON.parse(savedTank);
      const tank = state.allTanks.find(t => 
        t.name === tankData.name && 
        t.country === tankData.country && 
        t.type === tankData.type
      );
      
      if (tank) {
        state.selectedTank = tank;
        state.restoringTankFromReload = true;
        console.log('Restored selected tank:', tank.name);
        return tank;
      }
    } catch (error) {
      console.warn('Error restoring selected tank:', error);
      localStorage.removeItem('selectedTank');
    }
  }
  return null;
}

/**
 * Экспорт функций для глобального доступа (совместимость)
 */
window.showTankCharacteristics = showTankCharacteristics;
window.showTankCharacteristicsById = showTankCharacteristicsById;
window.hideTankCharacteristics = hideTankCharacteristics;
window.showTankList = showTankList;
