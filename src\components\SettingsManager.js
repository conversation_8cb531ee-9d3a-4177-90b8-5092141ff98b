/**
 * Менеджер настроек приложения
 * Отвечает за управление пользовательскими настройками
 */

export class SettingsManager {
  constructor() {
    this.settings = {
      theme: 'dark',
      language: 'ru',
      fontSize: 'medium',
      cacheEnabled: true
    };
    this.loadSettings();
  }

  /**
   * Загрузить настройки из localStorage
   */
  loadSettings() {
    Object.keys(this.settings).forEach(key => {
      const saved = localStorage.getItem(key);
      if (saved !== null) {
        this.settings[key] = key === 'cacheEnabled' ? saved === 'true' : saved;
      }
    });
  }

  /**
   * Сохранить настройку
   */
  saveSetting(key, value) {
    this.settings[key] = value;
    localStorage.setItem(key, value);
  }

  /**
   * Получить настройку
   */
  getSetting(key) {
    return this.settings[key];
  }

  /**
   * Применить тему
   */
  applyTheme(theme) {
    this.saveSetting('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);

    if (theme === 'dark') {
      document.body.classList.add('gray-bg');
    } else {
      document.body.classList.remove('gray-bg');
    }

    console.log('Theme applied:', theme);
  }

  /**
   * Применить размер шрифта
   */
  applyFontSize(fontSize) {
    this.saveSetting('fontSize', fontSize);

    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px',
    };

    document.documentElement.style.setProperty(
      '--base-font-size', 
      fontSizeMap[fontSize] || '16px'
    );

    console.log('Font size applied:', fontSize);
  }

  /**
   * Применить язык
   */
  applyLanguage(language) {
    this.saveSetting('language', language);
    console.log('Language applied:', language);
    // Здесь будет логика смены языка
  }

  /**
   * Очистить кэш
   */
  async clearCache() {
    if (!confirm('Вы уверены, что хотите очистить весь кэш приложения?')) {
      return;
    }

    try {
      // Очистка localStorage и sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // Очистка кэша браузера
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      }

      alert('Кэш успешно очищен. Страница будет перезагружена.');
      location.reload();
    } catch (error) {
      console.error('Error clearing cache:', error);
      alert('Ошибка при очистке кэша');
    }
  }

  /**
   * Инициализация дропдаунов настроек
   */
  initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.custom-dropdown');

    dropdowns.forEach(dropdown => {
      const selected = dropdown.querySelector('.dropdown-selected');
      const optionElements = dropdown.querySelectorAll('.dropdown-option');

      // Устанавливаем текущее значение
      const dropdownType = dropdown.dataset.dropdown;
      const currentValue = this.getSetting(dropdownType);
      
      if (currentValue) {
        const option = dropdown.querySelector(`[data-value="${currentValue}"]`);
        if (option && selected) {
          selected.dataset.value = currentValue;
          const textElement = selected.querySelector('.dropdown-text');
          if (textElement) {
            textElement.textContent = option.textContent.replace('✓', '').trim();
          }
          
          optionElements.forEach(opt => {
            opt.classList.toggle('selected', opt.dataset.value === currentValue);
          });
        }
      }

      // Обработчик клика по выбранному элементу
      selected?.addEventListener('click', e => {
        e.stopPropagation();

        // Закрываем другие дропдауны
        dropdowns.forEach(otherDropdown => {
          if (otherDropdown !== dropdown) {
            otherDropdown.classList.remove('active');
            otherDropdown.querySelector('.dropdown-selected')?.classList.remove('active');
          }
        });

        dropdown.classList.toggle('active');
        selected.classList.toggle('active');
      });

      // Обработчики кликов по опциям
      optionElements.forEach(option => {
        option.addEventListener('click', e => {
          e.stopPropagation();

          const value = option.dataset.value;
          const text = option.textContent.replace('✓', '').trim();
          const dropdownType = dropdown.dataset.dropdown;

          if (selected) {
            selected.dataset.value = value;
            const textElement = selected.querySelector('.dropdown-text');
            if (textElement) {
              textElement.textContent = text;
            }
          }

          optionElements.forEach(opt => opt.classList.remove('selected'));
          option.classList.add('selected');

          dropdown.classList.remove('active');
          selected?.classList.remove('active');

          this.handleDropdownChange(dropdownType, value);
        });
      });
    });

    // Закрытие дропдаунов при клике вне их
    document.addEventListener('click', () => {
      dropdowns.forEach(dropdown => {
        dropdown.classList.remove('active');
        dropdown.querySelector('.dropdown-selected')?.classList.remove('active');
      });
    });
  }

  /**
   * Обработка изменений в дропдаунах
   */
  handleDropdownChange(type, value) {
    switch (type) {
      case 'theme':
        this.applyTheme(value);
        break;
      case 'language':
        this.applyLanguage(value);
        break;
      case 'font-size':
        this.applyFontSize(value);
        break;
      default:
        console.warn('Unknown dropdown type:', type);
    }
  }

  /**
   * Инициализация переключателей
   */
  initializeToggles() {
    const cacheToggle = document.getElementById('cache-toggle');
    if (cacheToggle) {
      cacheToggle.checked = this.getSetting('cacheEnabled');
      
      cacheToggle.addEventListener('change', e => {
        const enabled = e.target.checked;
        this.saveSetting('cacheEnabled', enabled);
        console.log('Cache enabled:', enabled);
      });
    }
  }

  /**
   * Инициализация кнопок
   */
  initializeButtons() {
    const clearCacheBtn = document.getElementById('clear-cache-btn');
    if (clearCacheBtn) {
      clearCacheBtn.addEventListener('click', () => this.clearCache());
    }
  }

  /**
   * Применить все сохраненные настройки
   */
  applyAllSettings() {
    this.applyTheme(this.getSetting('theme'));
    this.applyFontSize(this.getSetting('fontSize'));
    this.applyLanguage(this.getSetting('language'));
  }
}

// Создаем глобальный экземпляр
export const settingsManager = new SettingsManager();

/**
 * Инициализация обработчиков настроек
 */
export function initializeSettingsHandlers() {
  settingsManager.initializeDropdowns();
  settingsManager.initializeToggles();
  settingsManager.initializeButtons();
  settingsManager.applyAllSettings();
  
  console.log('✅ Settings handlers initialized');
}

/**
 * Обработчики для совместимости с существующим кодом
 */
export function handleThemeChange(theme) {
  settingsManager.applyTheme(theme);
}

export function handleLanguageChange(language) {
  settingsManager.applyLanguage(language);
}

export function handleFontSizeChange(fontSize) {
  settingsManager.applyFontSize(fontSize);
}
