// Импорт стилей
import './assets/styles/tailwind.css';

// Импорт утилит
import { initPerformanceOptimizations } from './utils/performance.js';
import { initTheme } from './utils/theme.js';

// Оптимизированная инициализация с lazy loading и батчингом
document.addEventListener('DOMContentLoaded', async () => {
  try {
    console.log('[Init] 🚀 Starting ULTRA-OPTIMIZED app initialization...');

    // Инициализируем оптимизации производительности в первую очередь
    initPerformanceOptimizations();

    // Инициализируем тему
    initTheme();

    // Инициализируем основные компоненты
    const { appInitializer } = await import('./core/AppInitializer.js');
    await appInitializer.initialize();

    // Инициализируем менеджер событий
    const { eventManager } = await import('./core/EventManager.js');
    eventManager.initialize();

    // Инициализируем менеджер танков
    const { tankManager } = await import('./core/TankManager.js');
    tankManager.setSearchIndex(appInitializer.getSearchIndex());

    // Инициализируем роутер характеристик танков
    const { initTankCharacteristicsRouter } = await import('./components/TankCharacteristics.js');
    initTankCharacteristicsRouter();

    // Устанавливаем глобальные переменные для совместимости
    window.searchIndex = appInitializer.getSearchIndex();
    window.getFilteredTanks = () => tankManager.getFilteredTanks();
    window.updateTankList = () => tankManager.updateTankList();
    window.forceShowTankList = () => tankManager.forceShowTankList();

    console.log('[Init] ✅ Optimized app initialization complete');
  } catch (error) {
    console.error('[Init] ❌ Error during initialization:', error);

    // Показываем сообщение об ошибке
    const errorHTML = `
      <div id="app-error-overlay" class="error-overlay">
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-text">Ошибка инициализации приложения</div>
          <button onclick="location.reload()" class="error-button">Обновить страницу</button>
        </div>
      </div>
    `;
    document.body.insertAdjacentHTML('beforeend', errorHTML);
  }
});





















