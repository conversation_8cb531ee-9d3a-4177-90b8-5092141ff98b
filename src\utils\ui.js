// UI utility functions for managing sections and sidebar
import { state } from '../store/state.js';

// Map sidebar menu keys to their corresponding container IDs/classes.
export const SECTION_MAP = {
  overview: 'overview-section', // General info page
  vehicles: 'tank-list', // Tank list grid (main content columns)
  compare: 'compare-section', // Compare tanks UI
  settings: 'settings-section', // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};

/**
 * Hide all main content containers defined in SECTION_MAP by
 * adding the `hidden` class and setting display to none.
 * Also closes any open tank detail windows.
 */
export function hideAllSections() {
  Object.values(SECTION_MAP).forEach(id => {
    const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
    if (el) {
      el.style.display = 'none';
      el.style.opacity = '0';
      el.style.visibility = 'hidden';
      el.classList.add('hidden');
    }
  });

  // Close tank detail windows when switching tabs
  closeTankDetailWindows();
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.display = 'none';
    tankCharacteristicsContainer.style.opacity = '0';
    tankCharacteristicsContainer.style.visibility = 'hidden';
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Show a specific section based on sidebar menu key. Uses sensible default
 * display types: `grid` for the tank list and `block` for everything else.
 * @param {string} menu - One of the keys of SECTION_MAP (overview | vehicles | compare | settings)
 */
export function showSection(menu) {
  const id = SECTION_MAP[menu];
  if (!id) return;
  const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
  if (el) {
    // Используем CSS классы вместо !important
    el.style.display = menu === 'vehicles' ? 'grid' : 'block';
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.classList.remove('hidden');
    el.classList.add('section-visible');
  }

  // Для секции vehicles заполняем список танков
  // if (menu === 'vehicles' && typeof window.applyFiltersAndRenderTankList === 'function') {
    // // Используем requestAnimationFrame для плавного отображения
    // requestAnimationFrame(() => {
    //   window.applyFiltersAndRenderTankList();
    // });
  // }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Исправленная система вращения иконки
  const logo = sidebar.querySelector('.sidebar-logo-icon');
  if (logo) {
    let isHovering = false;
    let lastMouseX = 0;
    let currentRotation = 0;
    let spinInterval = null;
    let isSpinning = false;
    let spinSpeed = 1000;
    let stopTimeout = null;

    // Функция для получения текущего угла поворота
    const getCurrentRotation = () => {
      const transform = logo.style.transform;
      const match = transform.match(/rotate\((-?\d+(?:\.\d+)?)deg\)/);
      return match ? parseFloat(match[1]) : 0;
    };

    // Функция плавного вращения
    const startSpinning = (direction, speed = spinSpeed) => {
      if (isSpinning) return;

      isSpinning = true;
      const rotationStep = direction === 'clockwise' ? 360 : -360;

      const spin = () => {
        if (!isHovering || !isSpinning) return;

        currentRotation += rotationStep;
        logo.style.transition = `transform ${speed}ms linear`;
        logo.style.transform = `rotate(${currentRotation}deg)`;

        spinInterval = setTimeout(spin, speed);
      };

      spin();
    };

    // Функция смены направления
    const changeDirection = (newDirection) => {
      if (!isSpinning || !isHovering) return;

      clearTimeout(spinInterval);
      isSpinning = false;
      currentRotation = getCurrentRotation();

      setTimeout(() => {
        if (isHovering) {
          startSpinning(newDirection, spinSpeed);
        }
      }, 50);
    };

    // Обработчик движения мыши с ПРАВИЛЬНОЙ логикой направления
    const handleMouseMove = (e) => {
      if (!isHovering) return;

      const currentMouseX = e.clientX;
      const deltaX = currentMouseX - lastMouseX;

      if (Math.abs(deltaX) > 3) {
        // ИСПРАВЛЕННАЯ ЛОГИКА: движение вправо = против часовой (как толкание)
        const newDirection = deltaX > 0 ? 'counterclockwise' : 'clockwise';
        changeDirection(newDirection);
      }

      lastMouseX = currentMouseX;
    };

    // ИСПРАВЛЕННАЯ функция остановки - БЕЗ дополнительных оборотов
    const stopSpinning = () => {
      isHovering = false;

      // Очищаем все таймауты и интервалы
      clearTimeout(spinInterval);
      clearTimeout(stopTimeout);
      isSpinning = false;

      // Получаем текущий угол и СРАЗУ останавливаемся
      currentRotation = getCurrentRotation();

      // Плавно возвращаемся к 0 БЕЗ дополнительных оборотов
      logo.style.transition = 'transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      logo.style.transform = 'rotate(0deg)';
      currentRotation = 0;

      // Сброс стилей
      setTimeout(() => {
        if (!isHovering) {
          logo.style.transition = '';
          logo.style.transform = '';
        }
      }, 1200);
    };

    // События
    logo.addEventListener('mouseenter', (e) => {
      if (isHovering) return;

      // Очищаем таймаут остановки если он есть
      clearTimeout(stopTimeout);

      isHovering = true;
      lastMouseX = e.clientX;
      currentRotation = getCurrentRotation();

      // 30% шанс супер-быстрого вращения
      const isSuperFast = Math.random() < 0.3;
      spinSpeed = isSuperFast ? 400 : 1000;

      if (isSuperFast) {
        console.log('🚀 Супер-быстрое вращение активировано! (30% шанс)');
      }

      // Случайное направление
      const randomDirection = Math.random() < 0.5 ? 'clockwise' : 'counterclockwise';
      startSpinning(randomDirection, spinSpeed);
    });

    logo.addEventListener('mousemove', handleMouseMove);

    logo.addEventListener('mouseleave', () => {
      // Немедленная остановка без задержки
      stopSpinning();
    });
  }

  // Attach delegated click handler
  sidebar.addEventListener('click', e => {
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) return;
    e.preventDefault();
    const section = item.getAttribute('data-section');
    if (!section) return;
    let menu = section;
    // If Vehicles is already active and clicked again, switch to overview (toggle off)
    if (menu === 'vehicles' && state.currentMenuName === 'vehicles') {
      menu = 'overview';
    }
    onMenuSelected(menu);
  });

  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

function onMenuSelected(menu, _initial = false) {
  localStorage.setItem('activeMenuItem', menu);
  state.currentMenuName = menu;
  updateActiveClass(menu);
  hideAllSections();
  showSection(menu);

  // Show/hide flag section with animation
  const flagSection = document.getElementById('flag-section');
  if (flagSection) {
    // flagSection.style.display = 'block'; // Removed to allow max-height transition
    if (menu === 'vehicles') {
      flagSection.classList.add('open');
    } else {
      flagSection.classList.remove('open');
    }
  }

  // Убираем дублирующийся вызов - список танков уже показывается в showSection()
}

function updateActiveClass(menu) {
  document.querySelectorAll('.sidebar-menu-item').forEach(item => {
    const section = item.getAttribute('data-section');
    if (!section) return;
    const isActive = section === menu;
    item.classList.toggle('active', isActive);
  });
}
