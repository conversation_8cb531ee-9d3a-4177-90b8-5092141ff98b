// UI utility functions for managing sections and sidebar
import { state } from '../store/state.js';

// Map sidebar menu keys to their corresponding container IDs/classes.
export const SECTION_MAP = {
  overview: 'overview-section', // General info page
  vehicles: 'tank-list', // Tank list grid (main content columns)
  compare: 'compare-section', // Compare tanks UI
  settings: 'settings-section', // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};

/**
 * Hide all main content containers defined in SECTION_MAP by
 * adding the `hidden` class and setting display to none.
 * Also closes any open tank detail windows.
 */
export function hideAllSections() {
  Object.values(SECTION_MAP).forEach(id => {
    const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
    if (el) {
      el.style.display = 'none';
      el.style.opacity = '0';
      el.style.visibility = 'hidden';
      el.classList.add('hidden');
    }
  });

  // Close tank detail windows when switching tabs
  closeTankDetailWindows();
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.display = 'none';
    tankCharacteristicsContainer.style.opacity = '0';
    tankCharacteristicsContainer.style.visibility = 'hidden';
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Show a specific section based on sidebar menu key. Uses sensible default
 * display types: `grid` for the tank list and `block` for everything else.
 * @param {string} menu - One of the keys of SECTION_MAP (overview | vehicles | compare | settings)
 */
export function showSection(menu) {
  const id = SECTION_MAP[menu];
  if (!id) return;
  const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
  if (el) {
    // Используем CSS классы вместо !important
    el.style.display = menu === 'vehicles' ? 'grid' : 'block';
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.classList.remove('hidden');
    el.classList.add('section-visible');
  }

  // Для секции vehicles заполняем список танков
  // if (menu === 'vehicles' && typeof window.applyFiltersAndRenderTankList === 'function') {
    // // Используем requestAnimationFrame для плавного отображения
    // requestAnimationFrame(() => {
    //   window.applyFiltersAndRenderTankList();
    // });
  // }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Интерактивная система вращения иконки с отслеживанием движения курсора
  const logo = sidebar.querySelector('.sidebar-logo-icon');
  if (logo) {
    let spinTimeout;
    let isSpinning = false;
    let currentDirection = 'clockwise';
    let lastMouseX = 0;
    let mouseTrackingActive = false;

    // Функция получения случайной скорости
    const getRandomSpeed = () => {
      const speeds = ['0.6s', '0.8s', '1.0s', '1.2s'];
      return speeds[Math.floor(Math.random() * speeds.length)];
    };

    // Функция запуска вращения с заданным направлением
    const startSpin = (direction = null, duration = null) => {
      if (!direction) {
        // Случайное направление при первом запуске
        currentDirection = Math.random() < 0.5 ? 'clockwise' : 'counterclockwise';
      } else {
        currentDirection = direction;
      }

      if (!duration) {
        duration = getRandomSpeed();
      }

      isSpinning = true;

      // Используем CSS переменные для плавной анимации
      logo.style.setProperty('--spin-duration', duration);
      logo.style.setProperty('--spin-direction', currentDirection === 'clockwise' ? '360deg' : '-360deg');
      logo.classList.add('spinning');
    };

    // Функция изменения направления вращения
    const changeDirection = (newDirection) => {
      if (!isSpinning || currentDirection === newDirection) return;

      currentDirection = newDirection;

      // Плавно меняем направление без остановки анимации
      logo.style.setProperty('--spin-direction', currentDirection === 'clockwise' ? '360deg' : '-360deg');

      // Добавляем небольшой эффект ускорения при смене направления
      const currentDuration = logo.style.getPropertyValue('--spin-duration') || '1s';
      const speedBoost = parseFloat(currentDuration) * 0.7; // Ускоряем на 30%
      logo.style.setProperty('--spin-duration', `${speedBoost}s`);

      // Возвращаем нормальную скорость через короткое время
      setTimeout(() => {
        if (isSpinning) {
          logo.style.setProperty('--spin-duration', currentDuration);
        }
      }, 300);
    };

    // Плавная остановка с замедлением
    const stopSpin = () => {
      if (!isSpinning) return;

      // Сначала замедляем вращение
      logo.style.setProperty('--spin-duration', '2s');
      logo.classList.add('slowing-down');

      // Через короткое время полностью останавливаем
      setTimeout(() => {
        isSpinning = false;
        mouseTrackingActive = false;
        logo.classList.remove('spinning', 'slowing-down');

        // Очень плавный возврат к исходному положению
        logo.style.transition = 'transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        logo.style.transform = 'rotate(0deg)';

        // Сброс стилей после анимации
        setTimeout(() => {
          logo.style.transition = '';
          logo.style.transform = '';
          logo.style.removeProperty('--spin-duration');
          logo.style.removeProperty('--spin-direction');
        }, 1500);
      }, 500);
    };

    // Обработчик движения мыши для определения направления "толчка"
    const handleMouseMove = (e) => {
      if (!mouseTrackingActive || !isSpinning) return;

      const currentMouseX = e.clientX;
      const deltaX = currentMouseX - lastMouseX;

      // Определяем направление движения курсора
      if (Math.abs(deltaX) > 2) { // Снижен порог для более чувствительной реакции
        const newDirection = deltaX > 0 ? 'clockwise' : 'counterclockwise';

        // Учитываем скорость движения курсора для более реалистичного эффекта
        const speed = Math.min(Math.abs(deltaX) / 10, 1); // Нормализуем скорость от 0 до 1
        const baseSpeed = parseFloat(logo.style.getPropertyValue('--spin-duration') || '1s');
        const adjustedSpeed = Math.max(baseSpeed * (1 - speed * 0.3), 0.3); // Ускоряем при быстром движении

        changeDirection(newDirection);

        // Временно ускоряем при быстром движении курсора
        if (speed > 0.5) {
          logo.style.setProperty('--spin-duration', `${adjustedSpeed}s`);
          setTimeout(() => {
            if (isSpinning) {
              logo.style.setProperty('--spin-duration', `${baseSpeed}s`);
            }
          }, 200);
        }
      }

      lastMouseX = currentMouseX;
    };

    // Обработчики событий
    logo.addEventListener('mouseenter', (e) => {
      clearTimeout(spinTimeout);
      lastMouseX = e.clientX;
      mouseTrackingActive = true;
      startSpin();
    });

    logo.addEventListener('mousemove', handleMouseMove);

    logo.addEventListener('mouseleave', () => {
      mouseTrackingActive = false;
      spinTimeout = setTimeout(stopSpin, 1000);
    });
  }

  // Attach delegated click handler
  sidebar.addEventListener('click', e => {
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) return;
    e.preventDefault();
    const section = item.getAttribute('data-section');
    if (!section) return;
    let menu = section;
    // If Vehicles is already active and clicked again, switch to overview (toggle off)
    if (menu === 'vehicles' && state.currentMenuName === 'vehicles') {
      menu = 'overview';
    }
    onMenuSelected(menu);
  });

  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

function onMenuSelected(menu, _initial = false) {
  localStorage.setItem('activeMenuItem', menu);
  state.currentMenuName = menu;
  updateActiveClass(menu);
  hideAllSections();
  showSection(menu);

  // Show/hide flag section with animation
  const flagSection = document.getElementById('flag-section');
  if (flagSection) {
    // flagSection.style.display = 'block'; // Removed to allow max-height transition
    if (menu === 'vehicles') {
      flagSection.classList.add('open');
    } else {
      flagSection.classList.remove('open');
    }
  }

  // Убираем дублирующийся вызов - список танков уже показывается в showSection()
}

function updateActiveClass(menu) {
  document.querySelectorAll('.sidebar-menu-item').forEach(item => {
    const section = item.getAttribute('data-section');
    if (!section) return;
    const isActive = section === menu;
    item.classList.toggle('active', isActive);
  });
}
