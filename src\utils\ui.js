// UI utility functions for managing sections and sidebar
import { state } from '../store/state.js';

// Map sidebar menu keys to their corresponding container IDs/classes.
export const SECTION_MAP = {
  overview: 'overview-section', // General info page
  vehicles: 'tank-list', // Tank list grid (main content columns)
  compare: 'compare-section', // Compare tanks UI
  settings: 'settings-section', // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};

/**
 * Hide all main content containers defined in SECTION_MAP by
 * adding the `hidden` class and setting display to none.
 * Also closes any open tank detail windows.
 */
export function hideAllSections() {
  Object.values(SECTION_MAP).forEach(id => {
    const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
    if (el) {
      el.style.display = 'none';
      el.style.opacity = '0';
      el.style.visibility = 'hidden';
      el.classList.add('hidden');
    }
  });

  // Close tank detail windows when switching tabs
  closeTankDetailWindows();
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.display = 'none';
    tankCharacteristicsContainer.style.opacity = '0';
    tankCharacteristicsContainer.style.visibility = 'hidden';
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Show a specific section based on sidebar menu key. Uses sensible default
 * display types: `grid` for the tank list and `block` for everything else.
 * @param {string} menu - One of the keys of SECTION_MAP (overview | vehicles | compare | settings)
 */
export function showSection(menu) {
  const id = SECTION_MAP[menu];
  if (!id) return;
  const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
  if (el) {
    // Используем CSS классы вместо !important
    el.style.display = menu === 'vehicles' ? 'grid' : 'block';
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.classList.remove('hidden');
    el.classList.add('section-visible');
  }

  // Для секции vehicles заполняем список танков
  // if (menu === 'vehicles' && typeof window.applyFiltersAndRenderTankList === 'function') {
    // // Используем requestAnimationFrame для плавного отображения
    // requestAnimationFrame(() => {
    //   window.applyFiltersAndRenderTankList();
    // });
  // }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Продвинутая система вращения с индивидуальным отслеживанием каждой линии
  const logo = sidebar.querySelector('.sidebar-logo-icon');
  if (logo) {
    console.log('✅ Иконка найдена, настраиваем индивидуальное отслеживание линий');

    let isSpinning = false;
    let animationId = null;
    let currentRotation = 0;
    let lastMouseX = 0;
    let lastMouseY = 0;

    // Получаем все 4 линии иконки
    const paths = logo.querySelectorAll('path');
    console.log(`🎯 Найдено ${paths.length} линий для отслеживания`);

    // Функция непрерывного вращения
    const spin = (direction, speed = 800) => {
      if (!isSpinning) return;

      const step = direction === 'clockwise' ? 360 : -360;
      currentRotation += step;

      logo.style.transition = `transform ${speed}ms linear`;
      logo.style.transform = `rotate(${currentRotation}deg)`;

      animationId = setTimeout(() => spin(direction, speed), speed);
    };

    // Функция определения направления толчка на основе позиции курсора
    const getDirectionFromPosition = (mouseX, mouseY, rect) => {
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      // Определяем, в какой части иконки находится курсор
      const deltaX = mouseX - centerX;
      const deltaY = mouseY - centerY;

      // Если движение больше по горизонтали
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Горизонтальное движение: право = против часовой, лево = по часовой
        return deltaX > 0 ? 'counterclockwise' : 'clockwise';
      } else {
        // Вертикальное движение: вниз = против часовой, вверх = по часовой
        return deltaY > 0 ? 'counterclockwise' : 'clockwise';
      }
    };

    // Функция запуска вращения
    const startSpin = (direction, speed = 800) => {
      if (isSpinning) {
        // Если уже крутится, просто меняем направление
        clearTimeout(animationId);
      }

      isSpinning = true;
      spin(direction, speed);
    };

    // Функция остановки
    const stopSpin = () => {
      isSpinning = false;
      clearTimeout(animationId);

      // Плавно возвращаемся к 0 с естественным замедлением
      logo.style.transition = 'transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      logo.style.transform = 'rotate(0deg)';
      currentRotation = 0;

      setTimeout(() => {
        if (!isSpinning) {
          logo.style.transition = '';
          logo.style.transform = '';
        }
      }, 1500);
    };

    // Обработчик для каждой линии
    paths.forEach((path, index) => {
      const colors = ['#ff2db3', '#ffa020', '#1eb83a', '#0a7fff'];
      const colorName = ['розовая', 'оранжевая', 'зелёная', 'синяя'];

      path.addEventListener('mouseenter', (e) => {
        console.log(`🎯 Наведение на ${colorName[index]} линию`);

        const rect = logo.getBoundingClientRect();
        const direction = getDirectionFromPosition(e.clientX, e.clientY, rect);

        // 30% шанс супер-быстрого вращения
        const isSuperFast = Math.random() < 0.3;
        const speed = isSuperFast ? 400 : 800;

        if (isSuperFast) {
          console.log('🚀 Супер-быстрое вращение!');
        }

        startSpin(direction, speed);
      });

      path.addEventListener('mousemove', (e) => {
        if (!isSpinning) return;

        const currentMouseX = e.clientX;
        const currentMouseY = e.clientY;
        const deltaX = currentMouseX - lastMouseX;
        const deltaY = currentMouseY - lastMouseY;

        // Реагируем на движение курсора
        if (Math.abs(deltaX) > 3 || Math.abs(deltaY) > 3) {
          const rect = logo.getBoundingClientRect();
          const newDirection = getDirectionFromPosition(currentMouseX, currentMouseY, rect);

          console.log(`🔄 ${colorName[index]} линия: направление ${newDirection}`);

          clearTimeout(animationId);
          spin(newDirection, 600);
        }

        lastMouseX = currentMouseX;
        lastMouseY = currentMouseY;
      });

      path.addEventListener('mouseleave', () => {
        console.log(`👋 Курсор ушёл с ${colorName[index]} линии`);
        setTimeout(stopSpin, 800);
      });
    });

    // Глобальный обработчик для свайпов по области иконки
    const handleGlobalSwipe = (e) => {
      if (isSpinning) return; // Не мешаем, если уже крутится

      const rect = logo.getBoundingClientRect();
      const margin = 20;

      // Проверяем, что курсор в расширенной области иконки
      if (e.clientX >= rect.left - margin &&
          e.clientX <= rect.right + margin &&
          e.clientY >= rect.top - margin &&
          e.clientY <= rect.bottom + margin) {

        const deltaX = e.clientX - lastMouseX;
        const deltaY = e.clientY - lastMouseY;

        // Детекция резкого движения
        if (Math.abs(deltaX) > 15 || Math.abs(deltaY) > 15) {
          const swipeDirection = getDirectionFromPosition(e.clientX, e.clientY, rect);

          // 40% шанс быстрого вращения при свайпе
          const isFastSwipe = Math.random() < 0.4;
          const swipeSpeed = isFastSwipe ? 300 : 700;

          console.log(`💨 Свайп! Направление: ${swipeDirection}, ${isFastSwipe ? 'быстро' : 'медленно'}`);

          startSpin(swipeDirection, swipeSpeed);

          // Автоматическая остановка через 3 оборота
          setTimeout(() => {
            console.log('🛑 Остановка после свайпа');
            stopSpin();
          }, swipeSpeed * 3);
        }
      }

      lastMouseX = e.clientX;
      lastMouseY = e.clientY;
    };

    // Добавляем глобальный обработчик
    document.addEventListener('mousemove', handleGlobalSwipe);

  } else {
    console.error('❌ Иконка .sidebar-logo-icon не найдена!');
  }

  // Attach delegated click handler
  sidebar.addEventListener('click', e => {
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) return;
    e.preventDefault();
    const section = item.getAttribute('data-section');
    if (!section) return;
    let menu = section;
    // If Vehicles is already active and clicked again, switch to overview (toggle off)
    if (menu === 'vehicles' && state.currentMenuName === 'vehicles') {
      menu = 'overview';
    }
    onMenuSelected(menu);
  });

  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

function onMenuSelected(menu, _initial = false) {
  localStorage.setItem('activeMenuItem', menu);
  state.currentMenuName = menu;
  updateActiveClass(menu);
  hideAllSections();
  showSection(menu);

  // Show/hide flag section with animation
  const flagSection = document.getElementById('flag-section');
  if (flagSection) {
    // flagSection.style.display = 'block'; // Removed to allow max-height transition
    if (menu === 'vehicles') {
      flagSection.classList.add('open');
    } else {
      flagSection.classList.remove('open');
    }
  }

  // Убираем дублирующийся вызов - список танков уже показывается в showSection()
}

function updateActiveClass(menu) {
  document.querySelectorAll('.sidebar-menu-item').forEach(item => {
    const section = item.getAttribute('data-section');
    if (!section) return;
    const isActive = section === menu;
    item.classList.toggle('active', isActive);
  });
}
