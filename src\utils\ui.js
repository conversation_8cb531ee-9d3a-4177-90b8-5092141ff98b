// UI utility functions for managing sections and sidebar
import { state } from '../store/state.js';

// Map sidebar menu keys to their corresponding container IDs/classes.
export const SECTION_MAP = {
  overview: 'overview-section', // General info page
  vehicles: 'tank-list', // Tank list grid (main content columns)
  compare: 'compare-section', // Compare tanks UI
  settings: 'settings-section', // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};

/**
 * Hide all main content containers defined in SECTION_MAP by
 * adding the `hidden` class and setting display to none.
 * Also closes any open tank detail windows.
 */
export function hideAllSections() {
  Object.values(SECTION_MAP).forEach(id => {
    const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
    if (el) {
      el.style.display = 'none';
      el.style.opacity = '0';
      el.style.visibility = 'hidden';
      el.classList.add('hidden');
    }
  });

  // Close tank detail windows when switching tabs
  closeTankDetailWindows();
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.display = 'none';
    tankCharacteristicsContainer.style.opacity = '0';
    tankCharacteristicsContainer.style.visibility = 'hidden';
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Show a specific section based on sidebar menu key. Uses sensible default
 * display types: `grid` for the tank list and `block` for everything else.
 * @param {string} menu - One of the keys of SECTION_MAP (overview | vehicles | compare | settings)
 */
export function showSection(menu) {
  const id = SECTION_MAP[menu];
  if (!id) return;
  const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
  if (el) {
    // Используем CSS классы вместо !important
    el.style.display = menu === 'vehicles' ? 'grid' : 'block';
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.classList.remove('hidden');
    el.classList.add('section-visible');
  }

  // Для секции vehicles заполняем список танков
  // if (menu === 'vehicles' && typeof window.applyFiltersAndRenderTankList === 'function') {
    // // Используем requestAnimationFrame для плавного отображения
    // requestAnimationFrame(() => {
    //   window.applyFiltersAndRenderTankList();
    // });
  // }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Качественная система вращения иконки с нуля
  const icon = sidebar.querySelector('.sidebar-logo-icon');
  if (!icon) {
    console.error('❌ Иконка не найдена');
    return;
  }

  console.log('✅ Настраиваем качественную систему вращения');

  // Состояние системы
  const state = {
    isActive: false,
    currentDirection: 1, // 1 = по часовой, -1 = против часовой
    animationFrame: null,
    rotation: 0,
    speed: 0,
    targetSpeed: 0,
    lastMouseX: 0
  };

  // Константы
  const CONFIG = {
    NORMAL_SPEED: 2.0,      // градусов за кадр (обычное вращение)
    FAST_SPEED: 3.5,        // градусов за кадр (30% шанс при наведении)
    SWIPE_SPEED: 5.0,       // градусов за кадр (40% шанс при свайпе)
    ACCELERATION: 0.08,     // скорость разгона/торможения (плавнее)
    MOUSE_THRESHOLD: 4,     // минимальное движение мыши для реакции
    SWIPE_THRESHOLD: 12     // минимальное движение для свайпа
  };

  // Основная функция анимации
  function animate() {
    if (!state.isActive) {
      // Плавное торможение после ухода курсора
      state.speed *= 0.95; // Торможение
      if (Math.abs(state.speed) < 0.02) {
        // Полная остановка, затем плавный возврат к 0 через CSS transition
        state.speed = 0;
        state.animationFrame = null; // Сбрасываем ID анимации

        console.log('🛑 Остановка, возврат в исходное положение');

        // Плавный возврат к 0 через CSS transition (БЕЗ вращения)
        icon.style.transition = 'transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        icon.style.transform = 'rotate(0deg)';
        state.rotation = 0;

        // Убираем transition после завершения
        setTimeout(() => {
          icon.style.transition = '';
        }, 1500);

        return;
      }
    } else {
      // Плавное изменение скорости к целевой
      const diff = state.targetSpeed - state.speed;
      state.speed += diff * CONFIG.ACCELERATION;
    }

    // Обновляем поворот
    state.rotation += state.speed * state.currentDirection;
    icon.style.transform = `rotate(${state.rotation}deg)`;

    // Продолжаем анимацию
    state.animationFrame = requestAnimationFrame(animate);
  }

  // Запуск вращения
  function startRotation(speed = CONFIG.NORMAL_SPEED) {
    // Останавливаем предыдущую анимацию если она есть
    if (state.animationFrame) {
      cancelAnimationFrame(state.animationFrame);
      state.animationFrame = null;
    }

    state.isActive = true;
    state.targetSpeed = speed;
    state.currentDirection = Math.random() < 0.5 ? 1 : -1;

    // 30% шанс супер-быстрого вращения
    if (Math.random() < 0.3) {
      state.targetSpeed = CONFIG.FAST_SPEED;
      console.log('🚀 Супер-быстрое вращение!');
    }

    console.log(`🔄 Начинаем вращение: ${state.currentDirection > 0 ? 'по часовой' : 'против часовой'}`);

    // Всегда запускаем новую анимацию
    animate();
  }

  // Остановка вращения
  function stopRotation() {
    state.isActive = false;
    state.targetSpeed = 0;
    console.log('🛑 Начинаем плавное торможение');
    // НЕ останавливаем анимацию здесь - она остановится сама в animate()
  }

  // Смена направления
  function changeDirection(mouseDirection) {
    if (!state.isActive) return;

    // mouseDirection: 1 = вправо, -1 = влево
    // Логика: движение вправо = против часовой (-1), влево = по часовой (1)
    const newDirection = -mouseDirection;

    if (newDirection !== state.currentDirection) {
      state.currentDirection = newDirection;
      console.log(`↔️ Смена направления: ${newDirection > 0 ? 'по часовой' : 'против часовой'}`);
    }
  }

  // Обработка свайпа с 40% шансом супер-быстрого вращения
  function handleSwipe(deltaX) {
    if (state.isActive) return;

    // Останавливаем предыдущую анимацию если она есть
    if (state.animationFrame) {
      cancelAnimationFrame(state.animationFrame);
      state.animationFrame = null;
    }

    const swipeDirection = deltaX > 0 ? -1 : 1; // вправо = против часовой, влево = по часовой
    const isFastSwipe = Math.random() < 0.4; // 40% шанс супер-быстрого вращения

    state.isActive = true;
    state.currentDirection = swipeDirection;
    state.targetSpeed = isFastSwipe ? CONFIG.SWIPE_SPEED : CONFIG.NORMAL_SPEED;

    const directionText = swipeDirection > 0 ? 'по часовой' : 'против часовой';
    const speedText = isFastSwipe ? 'СУПЕР-БЫСТРО' : 'нормально';

    console.log(`💨 Толчок ${deltaX > 0 ? 'ВПРАВО' : 'ВЛЕВО'} → ${directionText}, ${speedText}!`);

    if (isFastSwipe) {
      console.log('🚀 40% шанс сработал - супер-быстрое вращение!');
    }

    // Всегда запускаем новую анимацию
    animate();

    // Автостоп через 4 секунды для свайпов
    setTimeout(() => {
      if (state.isActive) {
        console.log('⏰ Автостоп свайпа - начинаем торможение');
        stopRotation();
      }
    }, 4000);
  }

  // События мыши
  icon.addEventListener('mouseenter', (e) => {
    console.log('🖱️ Наведение курсора - начинаем вращение');
    state.lastMouseX = e.clientX;
    startRotation();
  });

  icon.addEventListener('mousemove', (e) => {
    const deltaX = e.clientX - state.lastMouseX;

    if (Math.abs(deltaX) > CONFIG.MOUSE_THRESHOLD) {
      const mouseDirection = deltaX > 0 ? 1 : -1;
      changeDirection(mouseDirection);
      state.lastMouseX = e.clientX;
    }
  });

  icon.addEventListener('mouseleave', () => {
    console.log('👋 Курсор убран - начинаем плавное торможение');
    stopRotation();
  });

  // Глобальный обработчик для свайпов
  let globalLastX = 0;
  document.addEventListener('mousemove', (e) => {
    if (state.isActive) return; // Не мешаем активному вращению

    const rect = icon.getBoundingClientRect();
    const margin = 30;

    // Проверяем область вокруг иконки
    if (e.clientX >= rect.left - margin &&
        e.clientX <= rect.right + margin &&
        e.clientY >= rect.top - margin &&
        e.clientY <= rect.bottom + margin) {

      const deltaX = e.clientX - globalLastX;

      if (Math.abs(deltaX) > CONFIG.SWIPE_THRESHOLD) {
        handleSwipe(deltaX);
      }
    }

    globalLastX = e.clientX;
  });

  // Attach delegated click handler
  sidebar.addEventListener('click', e => {
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) return;
    e.preventDefault();
    const section = item.getAttribute('data-section');
    if (!section) return;
    let menu = section;
    // If Vehicles is already active and clicked again, switch to overview (toggle off)
    if (menu === 'vehicles' && state.currentMenuName === 'vehicles') {
      menu = 'overview';
    }
    onMenuSelected(menu);
  });

  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

function onMenuSelected(menu, _initial = false) {
  localStorage.setItem('activeMenuItem', menu);
  state.currentMenuName = menu;
  updateActiveClass(menu);
  hideAllSections();
  showSection(menu);

  // Show/hide flag section with animation
  const flagSection = document.getElementById('flag-section');
  if (flagSection) {
    // flagSection.style.display = 'block'; // Removed to allow max-height transition
    if (menu === 'vehicles') {
      flagSection.classList.add('open');
    } else {
      flagSection.classList.remove('open');
    }
  }

  // Убираем дублирующийся вызов - список танков уже показывается в showSection()
}

function updateActiveClass(menu) {
  document.querySelectorAll('.sidebar-menu-item').forEach(item => {
    const section = item.getAttribute('data-section');
    if (!section) return;
    const isActive = section === menu;
    item.classList.toggle('active', isActive);
  });
}
