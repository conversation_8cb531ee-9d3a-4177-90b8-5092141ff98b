/**
 * Централизованный менеджер событий
 * Отвечает за обработку всех пользовательских взаимодействий
 */

import { state } from '../store/state.js';
import { getCachedElement, getDebounced } from '../utils/performance.js';
import { updateFilterSelection } from '../components/tank-list/index.js';
import {
  RUSSIAN_TO_INTERNAL_TYPE_MAP,
  ENGLISH_TO_INTERNAL_TYPE_MAP,
} from '../services/FilterService.js';

export class EventManager {
  constructor() {
    this.isInitialized = false;
    this.searchInput = null;
    this.countryFilters = null;
    this.categoryFilters = null;
  }

  /**
   * Инициализация всех обработчиков событий
   */
  initialize() {
    if (this.isInitialized) return;

    this.cacheDOMElements();
    this.setupEventDelegation();
    this.setupSearchHandlers();
    this.setupFilterHandlers();
    this.setupModalHandlers();
    this.setupHashChangeHandler();

    this.isInitialized = true;
    console.log('✅ Event Manager initialized');
  }

  /**
   * Кэширование DOM элементов для событий
   */
  cacheDOMElements() {
    this.searchInput = getCachedElement('#tank-search');
    this.countryFilters = document.querySelectorAll('#nation-filter .filter-item');
    this.categoryFilters = document.querySelectorAll('#type-filter .filter-item');
  }

  /**
   * Настройка делегирования событий
   */
  setupEventDelegation() {
    const tankListElement = getCachedElement('#tank-list');
    
    if (tankListElement) {
      tankListElement.addEventListener('click', this.handleTankListClick.bind(this));
    }

    // Глобальные обработчики
    document.addEventListener('click', this.handleGlobalClick.bind(this));
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * Настройка обработчиков поиска
   */
  setupSearchHandlers() {
    if (this.searchInput) {
      const optimizedSearch = getDebounced('search', this.handleSearchOptimized.bind(this), 200);
      this.searchInput.addEventListener('input', optimizedSearch);

      this.searchInput.addEventListener('focus', () => {
        // Предзагрузка поискового индекса при фокусе
        import('../services/SearchService.js').then(module => {
          if (!window.searchIndex) {
            console.log('[Search] 🔍 Preloading search index on focus');
            window.searchIndex = new module.TankSearchIndex(state.allTanks);
          }
        });
      });
    }
  }

  /**
   * Настройка обработчиков фильтров
   */
  setupFilterHandlers() {
    if (this.countryFilters) {
      this.countryFilters.forEach(filterItem => {
        filterItem.addEventListener('click', (event) => {
          event.preventDefault();
          const countryValue = filterItem.dataset.country;
          if (countryValue) {
            this.handleCountrySelection(countryValue);
          }
        });
      });
    }

    if (this.categoryFilters) {
      this.categoryFilters.forEach(filterItem => {
        filterItem.addEventListener('click', (event) => {
          event.preventDefault();
          const categoryValue = filterItem.dataset.category;
          if (categoryValue) {
            this.handleCategorySelection(categoryValue);
          }
        });
      });
    }
  }

  /**
   * Настройка обработчиков модальных окон
   */
  setupModalHandlers() {
    const modal = getCachedElement('#build-modal');
    const closeModalBtn = getCachedElement('.close-modal');

    if (modal && closeModalBtn) {
      closeModalBtn.addEventListener('click', this.closeModal.bind(this));
    }
  }

  /**
   * Настройка обработчика изменения hash
   */
  setupHashChangeHandler() {
    window.addEventListener('hashchange', () => {
      const newHash = window.location.hash;
      if (newHash && newHash.startsWith('#')) {
        const tankName = decodeURIComponent(newHash.substring(1));
        import('../components/TankCharacteristics.js').then(module => {
          module.showTankCharacteristics(tankName);
        });
      } else {
        import('../utils/ui.js').then(module => {
          module.hideAllSections();
          module.showSection('vehicles');
        });
      }
    });
  }

  /**
   * Обработка кликов по списку танков
   */
  handleTankListClick(event) {
    const tankItem = event.target.closest('.tank-item');
    if (tankItem) {
      const tankName = tankItem.dataset.tankName;
      if (tankName) {
        window.location.hash = `#${encodeURIComponent(tankName)}`;
      }
    }
  }

  /**
   * Глобальный обработчик кликов
   */
  handleGlobalClick(event) {
    // Закрытие модальных окон при клике вне их
    const modal = getCachedElement('#build-modal');
    if (modal && event.target === modal) {
      this.closeModal();
    }

    // Закрытие дропдаунов
    const dropdowns = document.querySelectorAll('.custom-dropdown');
    dropdowns.forEach(dropdown => {
      if (!dropdown.contains(event.target)) {
        dropdown.classList.remove('active');
        dropdown.querySelector('.dropdown-selected')?.classList.remove('active');
      }
    });
  }

  /**
   * Обработка события перед закрытием страницы
   */
  handleBeforeUnload() {
    // Сохраняем состояние приложения
    const appState = {
      activeTab: state.currentMenuName || 'vehicles',
      vehiclesVisible: !getCachedElement('#tank-list')?.classList.contains('hidden'),
      timestamp: Date.now()
    };
    localStorage.setItem('appState', JSON.stringify(appState));
  }

  /**
   * Оптимизированная функция поиска
   */
  handleSearchOptimized(event) {
    const query = event.target.value.trim();

    if (state.searchQuery === query.toLowerCase()) return;

    console.log(`[Search] Optimized search for: "${query}"`);

    const startTime = performance.now();
    state.searchQuery = query.toLowerCase();

    // Применяем фильтры и обновляем список
    import('./TankManager.js').then(module => {
      module.tankManager.applyFiltersAndRenderTankList();
    });

    const endTime = performance.now();
    console.log(`[Search] Search completed in ${(endTime - startTime).toFixed(2)}ms`);
  }

  /**
   * Обработка выбора страны
   */
  handleCountrySelection(countryValue) {
    // Сброс выбранного танка если он был
    if (state.selectedTank) {
      console.log(`Country filter '${countryValue}' clicked. Resetting tank selection.`);
      state.selectedTank = null;
      localStorage.removeItem('selectedTank');
      window.location.hash = '';
    }

    // Обновляем состояние фильтра
    if (state.selectedCountry === countryValue || countryValue === 'all') {
      state.selectedCountry = 'all';
      state.countrySelectedManually = false;
    } else {
      state.selectedCountry = countryValue;
      state.countrySelectedManually = true;
    }

    // Сбрасываем другие фильтры
    state.selectedCategory = 'all';
    state.categorySelectedManually = false;
    state.searchQuery = '';
    if (this.searchInput) this.searchInput.value = '';

    // Применяем фильтры
    import('./TankManager.js').then(module => {
      module.tankManager.applyFiltersAndRenderTankList();
    });

    updateFilterSelection('country', state.selectedCountry);
    updateFilterSelection('category', state.selectedCategory);
  }

  /**
   * Обработка выбора категории
   */
  handleCategorySelection(categoryValue) {
    // Сброс выбранного танка если он был
    if (state.selectedTank) {
      console.log(`Category filter '${categoryValue}' clicked. Resetting tank selection.`);
      state.selectedTank = null;
      localStorage.removeItem('selectedTank');
      window.location.hash = '';
    }

    // Преобразуем категорию во внутренний формат
    const internalCategory =
      categoryValue === 'all'
        ? 'all'
        : RUSSIAN_TO_INTERNAL_TYPE_MAP[categoryValue] ||
          ENGLISH_TO_INTERNAL_TYPE_MAP[categoryValue] ||
          categoryValue;

    // Обновляем состояние фильтра
    if (state.selectedCategory === internalCategory || internalCategory === 'all') {
      state.selectedCategory = 'all';
      state.categorySelectedManually = false;
      updateFilterSelection('category', 'all');
    } else {
      state.selectedCategory = internalCategory;
      state.categorySelectedManually = true;
      updateFilterSelection('category', categoryValue);
    }

    // Сбрасываем поиск
    state.searchQuery = '';
    if (this.searchInput) this.searchInput.value = '';

    // Применяем фильтры
    import('./TankManager.js').then(module => {
      module.tankManager.applyFiltersAndRenderTankList();
    });
  }

  /**
   * Закрытие модального окна
   */
  closeModal() {
    const modal = getCachedElement('#build-modal');
    if (modal) {
      modal.classList.add('hidden');
      modal.setAttribute('aria-hidden', 'true');
    }
  }
}

// Экспортируем единственный экземпляр
export const eventManager = new EventManager();
