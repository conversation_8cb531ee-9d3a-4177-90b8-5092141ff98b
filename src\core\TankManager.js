/**
 * Менеджер танков
 * Отвечает за управление данными танков, фильтрацию и отображение
 */

import { state } from '../store/state.js';
import { getCachedElement } from '../utils/performance.js';
import { getFilteredTanks } from '../services/FilterService.js';
import { renderTankList, updateFilterSelection } from '../components/tank-list/index.js';
import { updateTankDetailsUI } from '../components/tank-details/TankDetails.js';
import { getTankIconPath } from '../utils/constants.js';
import {
  RUSSIAN_TO_INTERNAL_TYPE_MAP,
  ENGLISH_TO_INTERNAL_TYPE_MAP,
} from '../services/FilterService.js';

export class TankManager {
  constructor() {
    this.searchIndex = null;
  }

  /**
   * Установить поисковый индекс
   */
  setSearchIndex(searchIndex) {
    this.searchIndex = searchIndex;
  }

  /**
   * Создание ID танка на основе имени
   */
  createTankId(tankName) {
    const iconPath = getTankIconPath(tankName);
    return iconPath.split('/').pop().replace('.webp', '');
  }

  /**
   * Получить отфильтрованные танки
   */
  getFilteredTanks() {
    return getFilteredTanks(state, this.searchIndex);
  }

  /**
   * Обновить список танков
   */
  updateTankList() {
    const filteredTanks = this.getFilteredTanks();

    if (!state.selectedTank) {
      this.showTankList(filteredTanks);
      this.hideTankCharacteristics();
    } else {
      this.showTankCharacteristics(state.selectedTank);
      this.hideTankList();
    }

    this.updateErrorState(filteredTanks);
  }

  /**
   * Показать список танков
   */
  showTankList(filteredTanks) {
    const tankListElement = getCachedElement('#tank-list');
    
    if (tankListElement) {
      tankListElement.classList.remove('hidden');
      tankListElement.style.display = 'grid';
      tankListElement.style.opacity = '1';
      tankListElement.style.visibility = 'visible';

      renderTankList(filteredTanks, state.allTanks, this.handleTankSelection.bind(this), null);
    }

    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.add('hidden');
    }
  }

  /**
   * Скрыть список танков
   */
  hideTankList() {
    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      tankListElement.classList.add('hidden');
      tankListElement.style.display = 'none';
    }
  }

  /**
   * Показать характеристики танка
   */
  showTankCharacteristics(tank) {
    const characteristicsContainer = getCachedElement('#tank-characteristics-container');
    
    if (characteristicsContainer) {
      characteristicsContainer.classList.remove('hidden');
      characteristicsContainer.style.display = 'block';
      characteristicsContainer.style.visibility = 'visible';
      characteristicsContainer.style.opacity = '1';

      // Показываем структуру характеристик
      const characteristicsGrid = characteristicsContainer.querySelector('.characteristics-grid');
      if (characteristicsGrid) {
        characteristicsGrid.style.display = 'flex';

        const columns = characteristicsGrid.querySelectorAll('.characteristics-column');
        columns.forEach(column => {
          column.style.display = 'block';
          column.classList.remove('hidden');

          const header = column.querySelector('.characteristics-header');
          const content = column.querySelector('.characteristics-content');

          if (header) header.style.display = 'flex';
          if (content) content.style.display = 'block';
        });
      }
    }

    const backButton = getCachedElement('#back-to-list-btn');
    if (backButton) {
      backButton.classList.remove('hidden');
    }

    // Обновляем UI деталей танка
    updateTankDetailsUI(tank);
  }

  /**
   * Скрыть характеристики танка
   */
  hideTankCharacteristics() {
    const characteristicsContainer = getCachedElement('#tank-characteristics-container');
    if (characteristicsContainer) {
      characteristicsContainer.classList.add('hidden');
      characteristicsContainer.style.display = 'none';
      characteristicsContainer.style.opacity = '0';
      characteristicsContainer.style.visibility = 'hidden';
    }
  }

  /**
   * Обновить состояние ошибки
   */
  updateErrorState(filteredTanks) {
    const tankError = getCachedElement('#tank-error');
    if (tankError) {
      const shouldShowError =
        !state.selectedTank && state.searchQuery.length > 0 && filteredTanks.length === 0;
      tankError.classList.toggle('hidden', !shouldShowError);
      if (shouldShowError) tankError.textContent = 'Танк не найден';
    }
  }

  /**
   * Обработка выбора танка
   */
  async handleTankSelection(tankName, forceSelect = false) {
    const tank = state.allTanks.find(t => t.name === tankName);

    if (!tank) {
      console.error(`Tank ${tankName} not found.`);
      return;
    }

    const isSelectedTank = state.selectedTank && state.selectedTank.name === tank.name;

    if (state.restoringTankFromReload) {
      console.log('Восстановление танка после F5, устанавливаем forceSelect');
      forceSelect = true;
    }

    if (isSelectedTank && !forceSelect) {
      // Отменяем выбор танка
      state.selectedTank = null;
      localStorage.removeItem('selectedTank');
      this.updateTankList();
      return;
    }

    // Выбираем танк
    state.selectedTank = tank;

    // Сохраняем в localStorage
    const tankToSave = {
      name: tank.name,
      country: tank.country,
      type: tank.type,
      countrySelectedManually: state.countrySelectedManually,
      categorySelectedManually: state.categorySelectedManually,
      tankId: this.createTankId(tank.name),
    };
    localStorage.setItem('selectedTank', JSON.stringify(tankToSave));

    // Обновляем фильтры
    this.updateFiltersForTank(tank);

    // Обновляем отображение
    this.updateTankList();

    // Обновляем URL если не восстанавливаем состояние
    if (!forceSelect) {
      history.pushState(
        { tankName: tank.name },
        `Tank: ${tank.name}`,
        `#${encodeURIComponent(tank.name)}`
      );
    }

    state.restoringTankFromReload = false;
  }

  /**
   * Обновить фильтры для выбранного танка
   */
  updateFiltersForTank(tank) {
    if (tank.country) {
      state.selectedCountry = tank.country;
      const countryForUI = this.getCountryForUI(tank.country);
      updateFilterSelection('country', countryForUI);
    }

    if (tank.type) {
      const internalType =
        RUSSIAN_TO_INTERNAL_TYPE_MAP[tank.type] ||
        ENGLISH_TO_INTERNAL_TYPE_MAP[tank.type] ||
        tank.type;
      state.selectedCategory = internalType;
      updateFilterSelection('category', tank.type);
    }
  }

  /**
   * Преобразование названий стран из внутреннего формата в формат UI
   */
  getCountryForUI(internalCountry) {
    const countryMap = {
      USSR: 'ussr',
      Germany: 'germany',
      USA: 'usa',
      France: 'france',
      UK: 'uk',
      Czech: 'czech',
      China: 'china',
      Japan: 'japan',
      Poland: 'poland',
      Sweden: 'sweden',
      Italy: 'italy',
      International: 'international',
    };
    return countryMap[internalCountry] || internalCountry.toLowerCase();
  }

  /**
   * Применить фильтры и отобразить список танков
   */
  applyFiltersAndRenderTankList(keepHidden = false) {
    const hideForOtherTabs = state.currentMenuName && state.currentMenuName !== 'vehicles';
    const finalKeepHidden = keepHidden || hideForOtherTabs;

    const filteredTanks = this.getFilteredTanks();
    const selectedTankName = state.selectedTank ? state.selectedTank.name : null;

    // Скрываем детали танка
    this.hideTankCharacteristics();

    console.log(`Отображаем ${filteredTanks.length} танков после фильтрации`);

    // Рендерим список танков
    renderTankList(
      filteredTanks,
      state.allTanks,
      this.handleTankSelection.bind(this),
      selectedTankName
    );

    // Обновляем отображение фильтров
    const countryForUI = this.getCountryForUI(state.selectedCountry);
    updateFilterSelection('country', countryForUI);
    
    const categoryForUI =
      Object.keys(RUSSIAN_TO_INTERNAL_TYPE_MAP).find(
        key => RUSSIAN_TO_INTERNAL_TYPE_MAP[key] === state.selectedCategory
      ) || state.selectedCategory;
    updateFilterSelection('category', categoryForUI);

    // Управляем видимостью списка танков
    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      if (finalKeepHidden) {
        tankListElement.classList.add('hidden');
        tankListElement.style.display = 'none';
        tankListElement.style.opacity = '0';
        tankListElement.style.visibility = 'hidden';
      } else {
        tankListElement.classList.remove('hidden');
        tankListElement.style.display = 'grid';
        tankListElement.style.opacity = '1';
        tankListElement.style.visibility = 'visible';
      }
    }
  }

  /**
   * Показать характеристики танка по ID
   */
  showTankCharacteristicsById(tankId) {
    const tank = state.allTanks.find(t => this.createTankId(t.name) === tankId);
    if (!tank) {
      console.error(`Tank with ID ${tankId} not found`);
      return;
    }

    state.selectedTank = tank;
    window.currentTankId = tankId;

    import('../utils/ui.js').then(module => {
      module.hideAllSections();
    });

    this.showTankCharacteristics(tank);
  }

  /**
   * Принудительно показать список танков
   */
  forceShowTankList() {
    console.log('Forcing tank list display');

    state.selectedTank = null;
    window.appState = { selectedTank: null };

    const tankListElement = getCachedElement('#tank-list');
    if (tankListElement) {
      tankListElement.classList.remove('hidden');
      tankListElement.style.display = 'grid';
      tankListElement.style.opacity = '1';
      tankListElement.style.visibility = 'visible';

      const filteredTanks = this.getFilteredTanks();
      renderTankList(filteredTanks, state.allTanks, this.handleTankSelection.bind(this), null);
    }

    this.hideTankCharacteristics();

    const backBtn = getCachedElement('#back-icon');
    if (backBtn) {
      backBtn.classList.add('hidden');
    }

    return 'Tank list display forced';
  }
}

// Экспортируем единственный экземпляр
export const tankManager = new TankManager();
